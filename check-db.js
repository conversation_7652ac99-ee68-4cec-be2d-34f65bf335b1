// Check database tables and data
const mysql = require('mysql2');

const connection = mysql.createConnection({
  host: 'localhost',
  user: 'root',
  password: '',
  database: 'friction-tec',
});

async function checkDatabase() {
  return new Promise((resolve, reject) => {
    connection.connect((err) => {
      if (err) {
        console.error('Error connecting to the database:', err.stack);
        reject(err);
        return;
      }
      console.log('Connected to the database');

      // Check locations table
      connection.query('SELECT * FROM locations LIMIT 5', (err, locations) => {
        if (err) {
          console.error('Error querying locations:', err);
          reject(err);
          return;
        }
        console.log('\n=== LOCATIONS TABLE ===');
        console.log(locations);

        // Check products table
        connection.query('SELECT * FROM products LIMIT 5', (err, products) => {
          if (err) {
            console.error('Error querying products:', err);
            reject(err);
            return;
          }
          console.log('\n=== PRODUCTS TABLE ===');
          console.log(products);

          // Check location_etages table
          connection.query('SELECT * FROM location_etages LIMIT 5', (err, etages) => {
            if (err) {
              console.error('Error querying location_etages:', err);
              reject(err);
              return;
            }
            console.log('\n=== LOCATION_ETAGES TABLE ===');
            console.log(etages);

            // Check recent movements
            connection.query('SELECT * FROM movements ORDER BY created_at DESC LIMIT 5', (err, movements) => {
              if (err) {
                console.error('Error querying movements:', err);
                reject(err);
                return;
              }
              console.log('\n=== RECENT MOVEMENTS ===');
              console.log(movements);

              connection.end();
              resolve();
            });
          });
        });
      });
    });
  });
}

checkDatabase().catch(console.error);