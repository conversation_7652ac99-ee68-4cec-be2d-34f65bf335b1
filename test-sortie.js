// Test script to debug sortie movement issue
// Using built-in fetch (Node.js 18+)

async function testSortieMovement() {
    const testData = {
        product_type: 'matiere',
        product_id: 1, // Assuming product ID 1 exists
        status: 'Sortie',
        quantity: 10,
        location_id: 70, // Using specified location ID
        fabricationDate: '2024-01-01',
        expirationDate: '2024-12-31',
        date: new Date().toISOString().slice(0, 10),
        part_id: 50, // Using specified part ID instead of etage_id
        batch_number: 'TEST-BATCH-001',
        time: new Date().toLocaleTimeString('fr-FR')
    };

    console.log('Testing sortie movement with data:', JSON.stringify(testData, null, 2));

    try {
        const response = await fetch('http://localhost:3001/api/movements', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(testData),
        });

        console.log('Response status:', response.status);
        // console.log('Response headers:', response.headers.raw());

        if (response.ok) {
            const result = await response.json();
            console.log('Success! Response:', JSON.stringify(result, null, 2));
        } else {
            const error = await response.text();
            console.log('Error response:', error);
        }
    } catch (error) {
        console.error('Network error:', error);
    }
}

// Test entrée movement for comparison
async function testEntreeMovement() {
    const testData = {
        product_type: 'matiere',
        product_id: 1,
        status: 'Entrée',
        quantity: 10,
        location_id: 70,
        fabricationDate: '2024-01-01',
        expirationDate: '2024-12-31',
        date: new Date().toISOString().slice(0, 10),
        part_id: 50,
        batch_number: 'TEST-BATCH-002',
        time: new Date().toLocaleTimeString('fr-FR')
    };

    console.log('\nTesting entrée movement with data:', JSON.stringify(testData, null, 2));

    try {
        const response = await fetch('http://localhost:3001/api/movements', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(testData),
        });

        console.log('Response status:', response.status);

        if (response.ok) {
            const result = await response.json();
            console.log('Success! Response:', JSON.stringify(result, null, 2));
        } else {
            const error = await response.text();
            console.log('Error response:', error);
        }
    } catch (error) {
        console.error('Network error:', error);
    }
}

async function runTests() {
    console.log('=== Testing Sortie Movement ===');
    await testSortieMovement();

    console.log('\n=== Testing Entrée Movement ===');
    await testEntreeMovement();
}

runTests();