<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket Notification Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .notification {
            background: #f0f8ff;
            border: 1px solid #0066cc;
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .connected {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .disconnected {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        #notifications {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>WebSocket Notification Test</h1>
    
    <div id="status" class="status disconnected">
        Disconnected from WebSocket server
    </div>
    
    <div>
        <button onclick="connect()">Connect</button>
        <button onclick="disconnect()">Disconnect</button>
        <button onclick="testMovement()">Create Test Movement</button>
        <button onclick="clearNotifications()">Clear Notifications</button>
    </div>
    
    <h2>Real-time Notifications:</h2>
    <div id="notifications">
        <p>No notifications yet...</p>
    </div>
    
    <script src="/socket.io/socket.io.js"></script>
    <script>
        let socket = null;
        const statusDiv = document.getElementById('status');
        const notificationsDiv = document.getElementById('notifications');
        
        function updateStatus(connected) {
            if (connected) {
                statusDiv.textContent = 'Connected to WebSocket server';
                statusDiv.className = 'status connected';
            } else {
                statusDiv.textContent = 'Disconnected from WebSocket server';
                statusDiv.className = 'status disconnected';
            }
        }
        
        function addNotification(notification) {
            const notifDiv = document.createElement('div');
            notifDiv.className = 'notification';
            notifDiv.innerHTML = `
                <strong>Product:</strong> ${notification.product_name} (ID: ${notification.product_id})<br>
                <strong>Message:</strong> ${notification.message}<br>
                <strong>Time:</strong> ${new Date(notification.created_at).toLocaleString()}<br>
                <strong>Read:</strong> ${notification.is_read ? 'Yes' : 'No'}
            `;
            
            // Add to top of notifications
            if (notificationsDiv.children.length === 1 && notificationsDiv.children[0].textContent === 'No notifications yet...') {
                notificationsDiv.innerHTML = '';
            }
            notificationsDiv.insertBefore(notifDiv, notificationsDiv.firstChild);
        }
        
        function connect() {
            if (socket) {
                socket.disconnect();
            }
            
            socket = io('http://localhost:3001');
            
            socket.on('connect', () => {
                console.log('Connected to WebSocket server');
                updateStatus(true);
            });
            
            socket.on('disconnect', () => {
                console.log('Disconnected from WebSocket server');
                updateStatus(false);
            });
            
            socket.on('newNotification', (notification) => {
                console.log('Received notification:', notification);
                addNotification(notification);
            });
        }
        
        function disconnect() {
            if (socket) {
                socket.disconnect();
                socket = null;
            }
            updateStatus(false);
        }
        
        async function testMovement() {
            try {
                const response = await fetch('http://localhost:3001/api/movements', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        product_type: 'matiere',
                        product_id: 51,
                        status: 'Entrée',
                        quantity: Math.floor(Math.random() * 20) + 1,
                        location_id: 55,
                        batch_number: `TEST-${Date.now()}`,
                        fabricationDate: '2025-08-03',
                        expirationDate: '2025-12-03',
                        affect_stock: false
                    })
                });
                
                if (response.ok) {
                    console.log('Test movement created successfully');
                } else {
                    console.error('Failed to create test movement');
                }
            } catch (error) {
                console.error('Error creating test movement:', error);
            }
        }
        
        function clearNotifications() {
            notificationsDiv.innerHTML = '<p>No notifications yet...</p>';
        }
        
        // Auto-connect on page load
        connect();
    </script>
</body>
</html>
